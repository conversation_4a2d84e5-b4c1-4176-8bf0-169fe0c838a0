package com.yunqu.crawler.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yunqu.emergency.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 资费爬取任务对象 xty_crawler_task
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("xty_crawler_task")
public class XtyCrawlerTask extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 日期ID
     */
    private Integer dateId;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 运营商类型1电信2移动3联通5广电
     */
    private Integer entType;

    /**
     * 状态1进行中2已完成
     */
    private Integer status;

    /**
     * 运营商编码
     */
    private String operatorCode;

    /**
     * 运营商名称
     */
    private String operatorName;

    /**
     * 版本号
     */
    private String versionNo;

    private String runServer;

    private String memo;

    /**
     * 本省资费公示数量
     */
    private Integer localProvinceCount;

    /**
     * 集团资费公示数量
     */
    private Integer groupCount;

}
