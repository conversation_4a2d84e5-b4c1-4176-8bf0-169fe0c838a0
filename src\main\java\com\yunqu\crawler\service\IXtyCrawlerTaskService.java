package com.yunqu.crawler.service;

import java.util.Collection;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yunqu.crawler.domain.XtyCrawlerTask;
import com.yunqu.crawler.domain.bo.XtyCrawlerTaskBo;
import com.yunqu.crawler.domain.vo.XtyCrawlerTaskVo;
import com.yunqu.emergency.common.mybatis.core.page.PageQuery;
import com.yunqu.emergency.common.mybatis.core.page.TableDataInfo;

/**
 * 资费爬取任务Service接口
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
public interface IXtyCrawlerTaskService extends IService<XtyCrawlerTask> {

    /**
     * 查询资费爬取任务
     *
     * @param id 主键
     * @return 资费爬取任务
     */
    XtyCrawlerTaskVo queryById(Long id);

    /**
     * 分页查询资费爬取任务列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 资费爬取任务分页列表
     */
    TableDataInfo<XtyCrawlerTaskVo> queryPageList(XtyCrawlerTaskBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的资费爬取任务列表
     *
     * @param bo 查询条件
     * @return 资费爬取任务列表
     */
    List<XtyCrawlerTaskVo> queryList(XtyCrawlerTaskBo bo);

    /**
     * 新增资费爬取任务
     *
     * @param bo 资费爬取任务
     * @return 是否新增成功
     */
    Boolean insertByBo(XtyCrawlerTaskBo bo);

    /**
     * 修改资费爬取任务
     *
     * @param bo 资费爬取任务
     * @return 是否修改成功
     */
    Boolean updateByBo(XtyCrawlerTaskBo bo);

    /**
     * 校验并批量删除资费爬取任务信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 检查是否有任务正在运行
     *
     * @return 是否有任务正在运行
     */
    boolean checkAnyTaskIsRunning(String serverName);

    /**
     * 检查是否有任务正在运行
     *
     * @return 是否有任务正在运行
     */
    boolean checkAnyTaskIsRunning(List<String> operators, String serverName);

    /**
     * 获取一个任务
     *
     * @return 任务
     */
    XtyCrawlerTask take(List<String> operators, String versionNo, String serverName);

    /**
     * 检查任务是否正在运行
     *
     * @param taskId 任务id
     * @return 是否正在运行
     */
    boolean checkTaskIsRunning(Long taskId);

    /**
     * 更新任务状态
     *
     * @param taskId 任务id
     * @param status 状态
     * @return 是否更新成功
     */
    boolean updateById(Long taskId, Integer status, String memo);

    /**
     * 检查指定日期是否已存在任务
     * 用于幂等性检查，避免重复创建任务
     *
     * @param dateId 日期ID
     * @return 是否已存在任务
     */
    boolean existsTasksByDate(Integer dateId);
}
