# CrawlerTariffTask 幂等性优化方案

## 问题描述
CrawlerTariffTask.crawlerTariff()方法在多进程环境下存在重复创建任务的问题。虽然使用了分布式锁，但仍需要在业务层面增加幂等性检查。

## 当前流程分析
1. `crawlerTariff()` - 主入口方法
2. `executeOperatorCrawler()` - 执行各运营商爬取任务
3. `ICrawlerTariffService.crawler()` - 各运营商实现类创建具体任务

## 优化方案
在`executeOperatorCrawler()`方法开始处增加版本级别的幂等性检查：
- 检查当前日期和版本号是否已存在任务
- 如果存在则跳过执行，记录日志
- 如果不存在则继续原有流程

## 实施步骤
1. 在IXtyCrawlerTaskService中添加检查方法
2. 修改executeOperatorCrawler方法增加幂等性检查
3. 优化日志记录

## 技术细节
- 检查维度：dateId + versionNo
- 检查位置：executeOperatorCrawler方法开始处
- 保持现有分布式锁作为最后防线
