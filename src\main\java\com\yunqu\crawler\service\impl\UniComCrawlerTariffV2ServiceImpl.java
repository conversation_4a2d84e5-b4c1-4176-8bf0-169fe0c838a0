package com.yunqu.crawler.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.yunqu.crawler.base.CacheConstants;
import com.yunqu.crawler.base.Constants;
import com.yunqu.crawler.domain.XtyCrawlerTask;
import com.yunqu.crawler.domain.XtyTariffCrawlRecord;
import com.yunqu.crawler.enums.OperatorEnum;
import com.yunqu.crawler.error.CrawlerException;
import com.yunqu.crawler.event.CrawlerTaskEvent;
import com.yunqu.crawler.mapper.XtyCrawlerTaskMapper;
import com.yunqu.crawler.mapper.XtyTariffCrawlRecordMapper;
import com.yunqu.crawler.service.ICrawlerTariffService;
import com.yunqu.crawler.service.IXtyTariffProvinceService;
import com.yunqu.emergency.common.core.utils.SpringUtils;
import com.yunqu.emergency.common.core.utils.StringUtils;
import com.yunqu.emergency.common.mybatis.annotation.Dynamic;
import com.yunqu.emergency.common.redis.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.security.cert.X509Certificate;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 *
 * </p>
 *
 * @ClassName UniComCrawlerTariffV2ServiceImpl
 * <AUTHOR> Copy This Tag)
 * @Description TODO 描述文件用途
 * @Since create in 2025/6/19 9:55
 * @Version v1.0
 * @Copyright Copyright (c) 2025
 * @Company 广州云趣信息科技有限公司
 */
@Slf4j
@Service(Constants.OPERATOR_UNICOM + ICrawlerTariffService.BASE_NAME)
public class UniComCrawlerTariffV2ServiceImpl extends UnicomCrawlerTariffServiceImpl {

    @Value("${crawler.unicom-req-base-url}")
    private String unicomReqBaseUrl;


    @Autowired
    public UniComCrawlerTariffV2ServiceImpl(XtyCrawlerTaskMapper crawlerTaskMapper,
                                            IXtyTariffProvinceService tariffProvinceService, XtyTariffCrawlRecordMapper crawlerRecordMapper) {
        super(crawlerTaskMapper, tariffProvinceService, crawlerRecordMapper);
    }

    private static final String GET_PROVINCE_URL = "/servicequerybusiness/queryTariff/provinceCity";

    private static final String GET_TARIFF_TYPE_URL = "/servicequerybusiness/queryTariff/TariffMenuDataHomePageNew";

    private static final String GET_ALLQG_TARIFF_DICT_URL = "/servicequerybusiness/queryTariff/countryTariffQueryChange";

    /**
     * 获取个人资费详情
     */
    private static final String GET_PERSONAL_TARIFF_URL = "/servicequerybusiness/queryTariff/tariffDetailInfoChange";

    /**
     * 获取套餐详情
     */
    private static final String GET_OTHER_TARIFF_URL = "/servicequerybusiness/queryTariff/TariffMenuDataDetailHomePageChange";

    /**
     * 执行请求爬虫
     *
     * @param task 爬虫任务
     */
    @Override
    public void excueteRequestCrawler(XtyCrawlerTask task) {
        try {
            SpringUtils.getAopProxy(this).excuete(task, task.getVersionNo());
        } catch (CrawlerException e) {
            log.error(e.getMessage(), e);
            task.setStatus(0);
            task.setMemo(e.getMessage());
            crawlerTaskMapper.updateById(task);
        }
    }

    @Dynamic(version = "versionNo")
    @Transactional(rollbackFor = {Exception.class, CrawlerException.class})
    protected void excuete(XtyCrawlerTask task, String versionNo) {
        try {
            // 1:请求获取全国数据
            int nationwdeTariffCount = analysisNationwideData(task);

            // 2:请求获取省份数据
            int localTariffCount = analysisLocalProvinceData(task, task.getVersionNo());

            if (localTariffCount == 0 || nationwdeTariffCount == 0) {
                log.warn("{} [FILE-WARN] 本省资费或全网资费爬取异常, 忽略更新任务状态, 运营商：{}{}, localProvinceCount:{}, groupCount:{}", LOG_PREFIX, provincesName, OperatorEnum.UNICOM.getName(), localTariffCount, nationwdeTariffCount);
                throw new CrawlerException("本省资费或全网资费爬取异常");
            }

            task.setLocalProvinceCount(localTariffCount);
            task.setGroupCount(nationwdeTariffCount);
            task.setStatus(2);
            crawlerTaskMapper.updateById(task);
            SpringUtils.publishEvent(new CrawlerTaskEvent(task.getId()));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    protected int analysisLocalProvinceData(XtyCrawlerTask task, String versionNo) {
        log.info("开始爬取全国数据, 任务信息: {}", task);
        JSONObject provinceInfo = RedisUtils.getCacheMapValue(CacheConstants.UNICOM_PROVINCE_INFO_DICT_KEY,
                task.getProvinceCode());
        String provinceCode = provinceInfo.getStr("provinceCode");
        String provinceName = provinceInfo.getStr("provinceName");
        String taskProvinceName = provinceInfo.getStr("name");

        String crawlerProvinceCode = task.getProvinceCode();
        List<JSONObject> provinceList = getProvinceList(crawlerProvinceCode);
        if (CollectionUtil.isEmpty(provinceList))
            return 0;

        Long taskId = task.getId();
        List<String> constansKeys = new ArrayList<>();
        List<XtyTariffCrawlRecord> resultList = new ArrayList<>();
        int count = 0;
        String crawlerType = provinceName + "资费";
        for (JSONObject entries : provinceList) {
            String cityCode = entries.getStr("cityCode");
            String provCode = entries.getStr("provCode");
            List<JSONObject> tariffTypeList = getTariffTypeList("0", provCode, cityCode);

            for (JSONObject tariffType : tariffTypeList) {
                String nameOne = tariffType.getStr("nameOne");
                List<JSONObject> twoObjectDTOList = tariffType.getBeanList("twoObjectDTOList", JSONObject.class);
                if (twoObjectDTOList == null || twoObjectDTOList.isEmpty())
                    continue;
                for (JSONObject object : twoObjectDTOList) {
                    String nameTwo = object.getStr("nameTwo");
                    String sortTwo = object.getStr("sortTwo");
                    String linkFlag = object.getStr("linkFlag");
                    List<XtyTariffCrawlRecord> records = new ArrayList<>();
                    if (StringUtils.isNotBlank(linkFlag)) {
                        Map<String, Object> param = new HashMap<>();
                        param.put("duanlianjieabc", "");
                        param.put("channelCode", "");
                        param.put("serviceType", "");
                        param.put("saleChannel", "");
                        param.put("externalSources", "");
                        param.put("contactCode", "");
                        param.put("version", "WT");
                        param.put("isItNationwide", "0");
                        param.put("isRemove", "");
                        param.put("isKey", "");
                        String choose;
                        String type5g;
                        String isOld;
                        if (StringUtils.equals("5G", nameTwo)) {
                            choose = "qg";
                            type5g = "5G";
                            isOld = "";
                            param.put("choose", "qg");
                            param.put("type5g", "5G");
                        } else if (StringUtils.equals("4G", nameTwo)) {
                            choose = "qg";
                            type5g = "";
                            isOld = "";
                            param.put("choose", "qg");
                        } else {
                            choose = "";
                            type5g = "";
                            isOld = "0" + sortTwo;
                            param.put("isOld", isOld);
                        }
                        param.put("provinceId", provCode);
                        param.put("cityId", cityCode);

                        List<JSONObject> allQGDictList = getAllQGList(param);
                        if (allQGDictList == null || allQGDictList.isEmpty()) {
                            List<JSONObject> threeObjectList = object.getBeanList("threeObjectList", JSONObject.class);
                            if (threeObjectList == null || threeObjectList.isEmpty()) continue;
                            String ids = CollUtil.join(threeObjectList.stream().map(o -> o.getStr("idThird")).toList(),
                                    ",");

                            Map<String, String> getTariffParam = new HashMap<>();
                            getTariffParam.put("version", "WT");
                            getTariffParam.put("isItNationwide", "0");
                            getTariffParam.put("levelThreeMenuId", ids);
                            getTariffParam.put("page", "1");
                            getTariffParam.put("size", String.valueOf(threeObjectList.size()));
                            getTariffParam.put("provinceId", provCode);
                            getTariffParam.put("cityId", cityCode);
                            JSONArray otherTariffList = getOtherTariffList(getTariffParam);
                            System.out.println(nameOne + ":" + nameTwo);
                            System.out.println(otherTariffList.size());
                            records = analysisThreeDetailData(otherTariffList, taskId,
                                    task.getDateId(), taskProvinceName, nameOne, "", "",
                                    nameOne, nameTwo, "", task.getVersionNo(), provinceCode, provinceName);
                        } else {
                            List<JSONObject> list = allQGDictList.stream().map(o -> {
                                JSONObject result = new JSONObject();
                                result.put("id", o.getStr("id"));
                                result.put("packageNameCode", o.getStr("packagetypeid"));
                                result.put("tariffDetailId", o.getStr("detailid"));
                                result.put("choose", choose);
                                result.put("isRemove", "");
                                result.put("isComboSet", StringUtils.trimToEmpty(o.getStr("isComboSet")));
                                result.put("isKey", "");
                                result.put("isOld", isOld);
                                result.put("type5g", type5g);
                                result.put("provinceId", provCode);
                                result.put("cityId", cityCode);
                                return result;
                            }).toList();

                            Map<String, String> getTariffParam = new HashMap<>();
                            getTariffParam.put("duanlianjieabc", "");
                            getTariffParam.put("channelCode", "");
                            getTariffParam.put("serviceType", "");
                            getTariffParam.put("saleChannel", "");
                            getTariffParam.put("externalSources", "");
                            getTariffParam.put("contactCode", "");
                            getTariffParam.put("version", "WT");
                            getTariffParam.put("isItNationwide", "0");
                            getTariffParam.put("tariffDetailReq", JSONUtil.toJsonStr(list));
                            getTariffParam.put("page", "1");
                            getTariffParam.put("size", String.valueOf(list.size()));
                            JSONArray tariffList = getAllPersonalTariffList(getTariffParam);
                            records = analysisTariffDetailInfoList(tariffList, taskId,
                                    task.getDateId(), taskProvinceName, nameOne, "", "", nameOne, nameTwo, "",
                                    task.getVersionNo(), provinceCode, provinceName);
                        }
                    } else {
                        List<JSONObject> threeObjectList = object.getBeanList("threeObjectList", JSONObject.class);
                        if (threeObjectList == null || threeObjectList.isEmpty())
                            continue;
                        String ids = CollUtil.join(threeObjectList.stream().map(o -> o.getStr("idThird")).toList(),
                                ",");

                        Map<String, String> getTariffParam = new HashMap<>();
                        getTariffParam.put("version", "WT");
                        getTariffParam.put("isItNationwide", "0");
                        getTariffParam.put("levelThreeMenuId", ids);
                        getTariffParam.put("page", "1");
                        getTariffParam.put("size", String.valueOf(threeObjectList.size()));
                        getTariffParam.put("provinceId", provCode);
                        getTariffParam.put("cityId", cityCode);
                        JSONArray otherTariffList = getOtherTariffList(getTariffParam);
                        System.out.println(nameOne + ":" + nameTwo);
                        System.out.println(otherTariffList.size());
                        records = analysisThreeDetailData(otherTariffList, taskId,
                                task.getDateId(), taskProvinceName, nameOne, "", "",
                                nameOne, nameTwo, "", task.getVersionNo(), provinceCode, provinceName);
                    }

                    if (records != null && !records.isEmpty()) {
                        for (XtyTariffCrawlRecord record : records) {
                            String name = StringUtils.trimToEmpty(record.getName());
                            String tariffNo = StringUtils.trimToEmpty(record.getTariffNo());
                            String contantsKey = name + tariffNo;
                            if (constansKeys.contains(contantsKey)) {
                                continue;
                            }
                            constansKeys.add(contantsKey);
                            record.setCrawlerType(crawlerType);
                            resultList.add(record);
                        }
                    }
                }
            }
        }
        if (!resultList.isEmpty()) {
            crawlerRecordMapper.insertBatch(resultList);
        }

        return resultList.size();
    }

    public int analysisNationwideData(XtyCrawlerTask task) throws IOException {
        JSONObject provinceInfo = RedisUtils.getCacheMapValue(CacheConstants.UNICOM_PROVINCE_INFO_DICT_KEY,
                task.getProvinceCode());
        String provinceCode = provinceInfo.getStr("provinceCode");
        String provinceName = provinceInfo.getStr("provinceName");
        String taskProvinceName = provinceInfo.getStr("name");
        // 获取所有资费分类数据字典
        Long taskId = task.getId();
        List<JSONObject> tariffTypeList = getTariffTypeList("1", "", "");
        int count = 0;
        String crawlerType = "全网资费";
        List<XtyTariffCrawlRecord> resultList = new ArrayList<>();
        for (JSONObject entries : tariffTypeList) {
            String nameOne = entries.getStr("nameOne");
            List<JSONObject> twoObjectDTOList = entries.getBeanList("twoObjectDTOList", JSONObject.class);
            if (twoObjectDTOList == null || twoObjectDTOList.isEmpty())
                continue;
            for (JSONObject object : twoObjectDTOList) {
                String nameTwo = object.getStr("nameTwo");
                String sortTwo = object.getStr("sortTwo");
                String linkFlag = object.getStr("linkFlag");
                if (StringUtils.isNotBlank(linkFlag)) {
                    Map<String, Object> param = new HashMap<>();
                    param.put("duanlianjieabc", "");
                    param.put("channelCode", "");
                    param.put("serviceType", "");
                    param.put("saleChannel", "");
                    param.put("externalSources", "");
                    param.put("contactCode", "");
                    param.put("version", "WT");
                    param.put("isItNationwide", "1");
                    param.put("isRemove", "");
                    param.put("isKey", "");
                    String choose;
                    String type5g;
                    String isOld;
                    if (StringUtils.equals("5G", nameTwo)) {
                        choose = "qg";
                        type5g = "5G";
                        isOld = "";
                        param.put("choose", "qg");
                        param.put("type5g", "5G");
                    } else if (StringUtils.equals("4G", nameTwo)) {
                        choose = "qg";
                        type5g = "";
                        isOld = "";
                        param.put("choose", "qg");
                    } else {
                        choose = "";
                        type5g = "";
                        isOld = "0" + sortTwo;
                        param.put("isOld", isOld);
                    }
                    List<JSONObject> allQGDictList = getAllQGList(param);
                    if (allQGDictList == null || allQGDictList.isEmpty()) {
                        List<JSONObject> threeObjectList = object.getBeanList("threeObjectList", JSONObject.class);
                        if (threeObjectList == null || threeObjectList.isEmpty()) continue;
                        String ids = CollUtil.join(threeObjectList.stream().map(o -> o.getStr("idThird")).toList(), ",");

                        Map<String, String> getTariffParam = new HashMap<>();
                        getTariffParam.put("version", "WT");
                        getTariffParam.put("isItNationwide", "1");
                        getTariffParam.put("levelThreeMenuId", ids);
                        getTariffParam.put("page", "1");
                        getTariffParam.put("size", String.valueOf(threeObjectList.size()));
                        JSONArray otherTariffList = getOtherTariffList(getTariffParam);
                        System.out.println(nameOne + ":" + nameTwo);
                        System.out.println(otherTariffList.size());
                        count += otherTariffList.size();
                        List<XtyTariffCrawlRecord> records = analysisThreeDetailData(otherTariffList, taskId,
                                task.getDateId(), taskProvinceName, crawlerType, "", "",
                                nameOne, nameTwo, "", task.getVersionNo(), provinceCode, provinceName);
                        resultList.addAll(records);
                    } else {
                        List<JSONObject> list = allQGDictList.stream().map(o -> {
                            JSONObject result = new JSONObject();
                            result.put("id", o.getStr("id"));
                            result.put("packageNameCode", o.getStr("packagetypeid"));
                            result.put("tariffDetailId", o.getStr("detailid"));
                            result.put("choose", choose);
                            result.put("isRemove", "");
                            result.put("isComboSet", StringUtils.trimToEmpty(o.getStr("isComboSet")));
                            result.put("isKey", "");
                            result.put("isOld", isOld);
                            result.put("type5g", type5g);
                            return result;
                        }).toList();

                        Map<String, String> getTariffParam = new HashMap<>();
                        getTariffParam.put("duanlianjieabc", "");
                        getTariffParam.put("channelCode", "");
                        getTariffParam.put("serviceType", "");
                        getTariffParam.put("saleChannel", "");
                        getTariffParam.put("externalSources", "");
                        getTariffParam.put("contactCode", "");
                        getTariffParam.put("version", "WT");
                        getTariffParam.put("isItNationwide", "1");
                        getTariffParam.put("tariffDetailReq", JSONUtil.toJsonStr(list));
                        getTariffParam.put("page", "1");
                        getTariffParam.put("size", String.valueOf(list.size()));
                        JSONArray tariffList = getAllPersonalTariffList(getTariffParam);
                        count += tariffList.size();
                        List<XtyTariffCrawlRecord> records = analysisTariffDetailInfoList(tariffList, taskId,
                                task.getDateId(), taskProvinceName, crawlerType, "", "", nameOne, nameTwo, "",
                                task.getVersionNo(), provinceCode, provinceName);
                        resultList.addAll(records);

                    }
                } else {
                    List<JSONObject> threeObjectList = object.getBeanList("threeObjectList", JSONObject.class);
                    if (threeObjectList == null || threeObjectList.isEmpty())
                        continue;
                    String ids = CollUtil.join(threeObjectList.stream().map(o -> o.getStr("idThird")).toList(), ",");

                    Map<String, String> getTariffParam = new HashMap<>();
                    getTariffParam.put("version", "WT");
                    getTariffParam.put("isItNationwide", "1");
                    getTariffParam.put("levelThreeMenuId", ids);
                    getTariffParam.put("page", "1");
                    getTariffParam.put("size", String.valueOf(threeObjectList.size()));
                    JSONArray otherTariffList = getOtherTariffList(getTariffParam);
                    System.out.println(nameOne + ":" + nameTwo);
                    System.out.println(otherTariffList.size());
                    count += otherTariffList.size();
                    List<XtyTariffCrawlRecord> records = analysisThreeDetailData(otherTariffList, taskId,
                            task.getDateId(), taskProvinceName, crawlerType, "", "",
                            nameOne, nameTwo, "", task.getVersionNo(), provinceCode, provinceName);
                    resultList.addAll(records);
                }
            }
        }
        if (!resultList.isEmpty()) {
            crawlerRecordMapper.insertBatch(resultList);
        }

        return count;
    }

    private OkHttpClient createTrustAllClient() {
        try {
            TrustManager[] trustAllCerts = new TrustManager[]{
                    new X509TrustManager() {
                        @Override
                        public void checkClientTrusted(X509Certificate[] chain, String authType) {
                        }

                        @Override
                        public void checkServerTrusted(X509Certificate[] chain, String authType) {
                        }

                        @Override
                        public X509Certificate[] getAcceptedIssuers() {
                            return new X509Certificate[0];
                        }
                    }
            };

            SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());

            return new OkHttpClient.Builder()
                    .sslSocketFactory(sslContext.getSocketFactory(), (X509TrustManager) trustAllCerts[0])
                    .hostnameVerifier((hostname, session) -> true)
                    .build();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public List<JSONObject> getProvinceList(String provinceCode) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("duanlianjieabc", "");
            params.put("channelCode", "");
            params.put("serviceType", "");
            params.put("saleChannel", "");
            params.put("externalSources", "");
            params.put("contactCode", "");
            params.put("version", "WT");
            params.put("provinceId", provinceCode);

            log.info("开始获取省份列表>>>>>{}", JSONUtil.toJsonStr(params));
            OkHttpClient client = createTrustAllClient();
            MultipartBody.Builder builder = new MultipartBody.Builder().setType(MultipartBody.FORM);
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                builder.addFormDataPart(entry.getKey(), entry.getValue().toString());
            }
            MultipartBody body = builder.build();
            if (StringUtils.isBlank(unicomReqBaseUrl)) {
                unicomReqBaseUrl = "https://m.client.10010.com";
            }
            String url = unicomReqBaseUrl + GET_PROVINCE_URL;
            Request request = new Request.Builder()
                    .url(url)
                    .method("POST", body)
                    .addHeader("User-Agent", "Apifox/1.0.0 (https://apifox.com)")
                    .addHeader("Accept", "*/*")
                    .addHeader("Host", "m.client.10010.com")
                    .addHeader("Connection", "keep-alive")
                    .addHeader("Content-Type", "multipart/form-data; boundary=--------------------------837532276651801837590819")
                    .addHeader("Cookie", "SHAREJSESSIONID=" + IdUtil.simpleUUID().toUpperCase()
                            + "; servicequerybusiness=1750300808.36.62.798086; acw_tc=1a0c639417503115190087472e005beefb189ddb61612b52f4744792c41906")
                    .build();
            Response response = client.newCall(request).execute();
            String result = response.body().string();
            log.info("结束获取省份列表>>>>>{}", result);
            JSONObject entries = JSONUtil.parseObj(result);
            List<JSONObject> cityList = entries.getBeanList("cityList", JSONObject.class);
            if (cityList == null) return List.of();
            return cityList;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return List.of();
        }
    }

    public List<JSONObject> getTariffTypeList(String isItNationwide, String provinceCode, String cityId) {
        Map<String, String> params = new HashMap<>();
        params.put("duanlianjieabc", "");
        params.put("channelCode", "");
        params.put("serviceType", "");
        params.put("saleChannel", "");
        params.put("externalSources", "");
        params.put("contactCode", "");
        params.put("version", "WT");
        params.put("isItNationwide", isItNationwide);
        params.put("provinceId", provinceCode);
        params.put("cityId", cityId);

        try {
            OkHttpClient client = createTrustAllClient();
            MultipartBody.Builder builder = new MultipartBody.Builder().setType(MultipartBody.FORM);
            for (Map.Entry<String, String> stringObjectEntry : params.entrySet()) {
                builder.addFormDataPart(stringObjectEntry.getKey(), stringObjectEntry.getValue().toString());
            }
            MultipartBody body = builder.build();
            if (StringUtils.isBlank(unicomReqBaseUrl)) {
                unicomReqBaseUrl = "https://m.client.10010.com";
            }
            String url = unicomReqBaseUrl + GET_TARIFF_TYPE_URL;
            log.info("开始获取资费二级分类数据字典[{}]>>>>>{}", url, JSONUtil.toJsonStr(params));
            Request request = new Request.Builder()
                    .url(url)
                    .method("POST", body)
                    .addHeader("User-Agent", "Apifox/1.0.0 (https://apifox.com)")
                    .addHeader("Accept", "*/*")
                    .addHeader("Host", "m.client.10010.com")
                    .addHeader("Connection", "keep-alive")
                    .addHeader("Content-Type",
                            "multipart/form-data; boundary=--------------------------837532276651801837590819")
                    .addHeader("Cookie", "SHAREJSESSIONID=" + IdUtil.simpleUUID().toUpperCase()
                            + "; servicequerybusiness=1750300808.36.62.798086; acw_tc=1a0c639417503115190087472e005beefb189ddb61612b52f4744792c41906")
                    .build();
            Response response = client.newCall(request).execute();
            String result = response.body().string();
            log.info("结束获取资费二级分类数据字典>>>>>{}", result);
            JSONObject entries = JSONUtil.parseObj(result);
            List<JSONObject> oneTwoMenusList = entries.getBeanList("oneTwoMenusList", JSONObject.class);
            if (oneTwoMenusList == null) return List.of();
            return oneTwoMenusList;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return List.of();
    }

    /**
     * 获取所有个人资费字典
     *
     * @param isItNationwide 是否全国
     * @param provinceCode   省份编码
     * @param cityId         城市编码
     * @return
     */
    private JSONArray getAllPersonalTariffList(Map<String, String> params) {
        try {
            log.info("开始获取所有个人资费字典>>>>>{}", JSONUtil.toJsonStr(params));
            OkHttpClient client = createTrustAllClient();
            MultipartBody.Builder builder = new MultipartBody.Builder().setType(MultipartBody.FORM);
            for (Map.Entry<String, String> stringObjectEntry : params.entrySet()) {
                builder.addFormDataPart(stringObjectEntry.getKey(), stringObjectEntry.getValue().toString());
            }
            MultipartBody body = builder.build();
            String url = unicomReqBaseUrl + "/servicequerybusiness/queryTariff/tariffDetailInfoChange";
            Request request = new Request.Builder()
                    .url(url)
                    .method("POST", body)
                    .addHeader("User-Agent", "Apifox/1.0.0 (https://apifox.com)")
                    .addHeader("Accept", "*/*")
                    .addHeader("Host", "m.client.10010.com")
                    .addHeader("Connection", "keep-alive")
                    .addHeader("Content-Type",
                            "multipart/form-data; boundary=--------------------------837532276651801837590819")
                    .addHeader("Cookie", "SHAREJSESSIONID=" + IdUtil.simpleUUID().toUpperCase()
                            + "; servicequerybusiness=1750300808.36.62.798086; acw_tc=1a0c639417503115190087472e005beefb189ddb61612b52f4744792c41906")
                    .build();
            Response response = client.newCall(request).execute();
            String result = response.body().string();
            log.info("结束获取所有个人资费字典>>>>>{}", result);
            JSONObject entries = JSONUtil.parseObj(result);
            JSONArray jsonArray = entries.getJSONArray("tariffDetailInfoList");
            if (jsonArray == null) return new JSONArray();
            return jsonArray;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return new JSONArray();
    }

    /**
     * 获取所有套餐字典
     *
     * @param isItNationwide 是否全国
     * @param provinceCode   省份编码
     * @param cityId         城市编码
     * @return
     */
    private JSONArray getOtherTariffList(Map<String, String> params) {
        try {
            log.info("开始获取所有套餐字典>>>>>{}", JSONUtil.toJsonStr(params));
            OkHttpClient client = createTrustAllClient()
                    .newBuilder()
                    .callTimeout(Duration.of(10, ChronoUnit.MINUTES))
                    .connectTimeout(Duration.of(10, ChronoUnit.MINUTES))
                    .readTimeout(10, TimeUnit.MINUTES)
                    .build();

            MultipartBody.Builder builder = new MultipartBody.Builder().setType(MultipartBody.FORM);
            for (Map.Entry<String, String> stringObjectEntry : params.entrySet()) {
                builder.addFormDataPart(stringObjectEntry.getKey(), stringObjectEntry.getValue().toString());
            }
            MultipartBody body = builder.build();
            String url = unicomReqBaseUrl + "/servicequerybusiness/queryTariff/TariffMenuDataDetailHomePageChange";
            Request request = new Request.Builder()
                    .url(url)
                    .method("POST", body)
                    .addHeader("User-Agent", "Apifox/1.0.0 (https://apifox.com)")
                    .addHeader("Accept", "*/*")
                    .addHeader("Host", "m.client.10010.com")
                    .addHeader("Connection", "keep-alive")
                    .addHeader("Content-Type",
                            "multipart/form-data; boundary=--------------------------602299579848013316681103")
                    .addHeader("Cookie", "SHAREJSESSIONID=" + IdUtil.simpleUUID().toUpperCase()
                            + "; servicequerybusiness=1750300808.36.62.798086; acw_tc=1a0c650f17503136188766543e0057d1400407de2f11b010af0e6a3f1f6937")
                    .build();
            Response response = client.newCall(request).execute();
            String result = response.body().string();
            log.info("结束获取所有套餐字典>>>>>{}", result);
            JSONObject entries = JSONUtil.parseObj(result);
            JSONArray jsonArray = entries.getJSONArray("threeDetailDate");
            if (jsonArray == null) return new JSONArray();
            return jsonArray;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return new JSONArray();
    }

    /**
     * 获取所有套餐字典
     *
     * @param isItNationwide 是否全国
     * @param provinceCode   省份编码
     * @param cityId         城市编码
     * @return
     */
    public List<JSONObject> getAllQGList(Map<String, Object> params) {
        try {
            log.info("开始获取二级分类所有资费字典>>>>>{}", JSONUtil.toJsonStr(params));
            OkHttpClient client = createTrustAllClient();
            MultipartBody.Builder builder = new MultipartBody.Builder().setType(MultipartBody.FORM);
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                builder.addFormDataPart(entry.getKey(), entry.getValue().toString());
            }
            MultipartBody body = builder.build();
            if (StringUtils.isBlank(unicomReqBaseUrl)) {
                unicomReqBaseUrl = "https://m.client.10010.com";
            }
            String url = unicomReqBaseUrl + GET_ALLQG_TARIFF_DICT_URL;
            Request request = new Request.Builder()
                    .url(url)
                    .method("POST", body)
                    .addHeader("User-Agent", "Apifox/1.0.0 (https://apifox.com)")
                    .addHeader("Accept", "*/*")
                    .addHeader("Host", "m.client.10010.com")
                    .addHeader("Connection", "keep-alive")
                    .addHeader("Content-Type", "multipart/form-data; boundary=--------------------------837532276651801837590819")
                    .addHeader("Cookie", "SHAREJSESSIONID=" + IdUtil.simpleUUID().toUpperCase()
                            + "; servicequerybusiness=1750300808.36.62.798086; acw_tc=1a0c639417503115190087472e005beefb189ddb61612b52f4744792c41906")
                    .build();
            Response response = client.newCall(request).execute();
            String result = response.body().string();
            log.info("结束获取二级分类所有资费字典>>>>>{}", result);
            JSONObject entries = JSONUtil.parseObj(result);
            List<JSONObject> tariffTypeList = entries.getBeanList("tariffTypeList", JSONObject.class);
            if (tariffTypeList == null) return List.of();
            return tariffTypeList;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return List.of();
    }

    public static void main(String[] args) throws IOException {
        UniComCrawlerTariffV2ServiceImpl service = new UniComCrawlerTariffV2ServiceImpl(null, null, null);
        System.out.println(service.getTariffTypeList("1", "", ""));
    }
}
