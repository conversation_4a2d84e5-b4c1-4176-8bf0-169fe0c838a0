package com.yunqu.crawler.service.impl;

import com.yunqu.crawler.config.CrawlerTaskProperties;
import com.yunqu.crawler.constants.CrawlerConstants;
import com.yunqu.crawler.domain.XtyCrawlerTask;
import com.yunqu.crawler.domain.XtyCrawlerVersion;
import com.yunqu.crawler.enums.CrawlStatusEnum;
import com.yunqu.crawler.enums.OperatorEnum;
import com.yunqu.crawler.manager.CrawlerVersionManager;
import com.yunqu.crawler.service.ICrawlerTariffService;
import com.yunqu.crawler.service.ICrawlerTariffTaskService;
import com.yunqu.crawler.service.IXtyCrawlerTaskService;
import com.yunqu.emergency.common.core.utils.SpringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.Function;

/**
 * 爬虫任务业务服务实现类
 * 
 * 主要功能：
 * 1. 运营商爬取任务的执行逻辑
 * 2. 任务状态检查和管理
 * 3. 任务调度和分发
 * 
 * <AUTHOR>
 * @version 1.0
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class CrawlerTariffTaskServiceImpl implements ICrawlerTariffTaskService {

    private final IXtyCrawlerTaskService crawlerTaskService;
    private final CrawlerVersionManager versionManager;
    private final CrawlerTaskProperties taskProperties;

    /**
     * 按顺序执行各运营商的爬取任务
     */
    @Override
    public void executeOperatorCrawler(Integer dateId, String versionNo) {
        log.info("{} [OPERATOR-START] 开始执行各运营商爬取任务, dateId: {}, versionNo: {}",
                CrawlerConstants.LOG_PREFIX, dateId, versionNo);
        long startTime = System.currentTimeMillis();
        int successCount = 0;
        int failCount = 0;

        // 创建版本记录
        versionManager.createVersion(dateId, versionNo);

        // 按顺序执行各运营商的爬取任务
        for (OperatorEnum operator : OperatorEnum.values()) {
            if (executeSingleOperatorCrawler(operator.getName(), operator.getCode(),
                    (o) -> ICrawlerTariffService.crawler(dateId, o, versionNo))) {
                successCount++;
            } else {
                failCount++;
            }
        }

        log.info("{} [OPERATOR-END] 完成各运营商爬取任务, 成功: {}, 失败: {}, 总耗时: {}ms",
                CrawlerConstants.LOG_PREFIX, successCount, failCount, System.currentTimeMillis() - startTime);
    }

    /**
     * 执行单个运营商的爬取任务
     * 包含异常处理和日志记录
     *
     * @param operatorName 运营商名称
     * @param operatorCode 运营商代码
     * @param crawlerTask  爬取任务
     * @return true=执行成功，false=执行失败
     */
    private boolean executeSingleOperatorCrawler(String operatorName, String operatorCode, Function<String, String> crawlerTask) {
        log.info("{} [OPERATOR-PROCESS] ---------- 开始爬取【{}】资费信息 ----------",
                CrawlerConstants.LOG_PREFIX, operatorName);
        long startTime = System.currentTimeMillis();

        try {
            crawlerTask.apply(operatorCode);
            log.info("{} [OPERATOR-SUCCESS] 【{}】资费信息爬取完成, 耗时: {}ms",
                    CrawlerConstants.LOG_PREFIX, operatorName, System.currentTimeMillis() - startTime);
            return true;
        } catch (Exception e) {
            log.error("{} [OPERATOR-FAIL] 【{}】资费信息爬取失败: {}",
                    CrawlerConstants.LOG_PREFIX, operatorName, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查是否有正在运行的任务
     */
    @Override
    public boolean isTaskRunning() {
        log.debug("{} [CHECK-START] 检查是否有正在运行的任务", CrawlerConstants.LOG_PREFIX);
        long startTime = System.currentTimeMillis();

        try {
            boolean running = crawlerTaskService.checkAnyTaskIsRunning(taskProperties.getServerName());
            log.debug("{} [CHECK-END] 任务运行状态检查完成: {}, 耗时: {}ms",
                    CrawlerConstants.LOG_PREFIX, running ? "有任务运行中" : "无运行任务", System.currentTimeMillis() - startTime);
            return running;
        } catch (Exception e) {
            log.error("{} [CHECK-FAIL] 检查任务运行状态异常: {}", CrawlerConstants.LOG_PREFIX, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 处理溢出任务
     *
     * @param versionNo 版本号
     * @return 溢出任务，如果没有返回null
     */
    @Override
    public XtyCrawlerTask handleOverflowTask(String versionNo) {
        if (!taskProperties.hasOverflowOperator()) {
            return null;
        }

        List<String> overflowOperatorList = taskProperties.getOverflowOperatorList();
        boolean running = crawlerTaskService.checkAnyTaskIsRunning(overflowOperatorList, taskProperties.getServerName());
        if (running) {
            log.info("{} [OVERFLOW-SKIP] 当前服务器正在运行溢出任务", CrawlerConstants.LOG_PREFIX);
            return null;
        }
        return crawlerTaskService.take(overflowOperatorList, versionNo, taskProperties.getServerName());
    }

    /**
     * 获取下一个待执行的任务
     */
    @Override
    public XtyCrawlerTask getNextTask(List<String> operators, String versionNo) {
        log.debug("{} [NEXT-START] 获取下一个待执行任务", CrawlerConstants.LOG_PREFIX);
        long startTime = System.currentTimeMillis();
        try {
            XtyCrawlerTask task = null;
            for (String operator : operators) {
                task = crawlerTaskService.take(List.of(operator), versionNo, taskProperties.getServerName());
                if (task != null) {
                    break;
                }
            }
            if (task != null) {
                log.info("{} [NEXT-SUCCESS] 成功获取待执行任务: taskId={}, operator={}, province={}, 耗时: {}ms",
                        CrawlerConstants.LOG_PREFIX, task.getId(), task.getOperatorName(), task.getProvinceName(),
                        System.currentTimeMillis() - startTime);
            } else {
                log.debug("{} [NEXT-EMPTY] 无待执行任务, 耗时: {}ms", CrawlerConstants.LOG_PREFIX, System.currentTimeMillis() - startTime);
            }
            return task;
        } catch (Exception e) {
            log.error("{} [NEXT-FAIL] 获取待执行任务异常: {}", CrawlerConstants.LOG_PREFIX, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 执行具体的爬虫任务
     *
     * @param task 爬虫任务
     */
    @Override
    public void executeTask(XtyCrawlerTask task) {
        if (task == null) {
            log.warn("{} [TASK-SKIP] 跳过执行：任务为空", CrawlerConstants.LOG_PREFIX);
            return;
        }

        log.info("{} [TASK-START] 开始执行任务: taskId={}, operator={}, province={}",
                CrawlerConstants.LOG_PREFIX, task.getId(), task.getOperatorName(), task.getProvinceName());
        long startTime = System.currentTimeMillis();

        try {
            String operatorCode = task.getOperatorCode();
            String serviceBeanName = operatorCode + ICrawlerTariffService.BASE_NAME;

            log.debug("{} [TASK-PROCESS] 获取任务执行服务: {}", CrawlerConstants.LOG_PREFIX, serviceBeanName);
            ICrawlerTariffService service = SpringUtils.getBean(serviceBeanName, ICrawlerTariffService.class);

            service.excueteRequestCrawler(task);

            log.info("{} [TASK-SUCCESS] 任务执行完成: taskId={}, operator={}, province={}, 耗时: {}ms",
                    CrawlerConstants.LOG_PREFIX, task.getId(), task.getOperatorName(), task.getProvinceName(),
                    System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("{} [TASK-FAIL] 任务执行失败: taskId={}, operator={}, province={}, 错误: {}",
                    CrawlerConstants.LOG_PREFIX, task.getId(), task.getOperatorName(), task.getProvinceName(),
                    e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理任务执行完成后的版本状态更新
     *
     * @param currentVersion 当前版本信息
     * @param operators 运营商列表
     * @param versionNo 版本号
     */
    @Override
    public void handleTaskCompletion(XtyCrawlerVersion currentVersion, List<String> operators, String versionNo) {
        CrawlStatusEnum currentStatus = CrawlStatusEnum.getByCode(currentVersion.getCrawlStatus());
        
        // 获取待执行的任务
        XtyCrawlerTask task = getNextTask(operators, versionNo);
        if (task == null) {
            log.debug("{} [EXECUTE-EMPTY] 当前没有待执行的任务", CrawlerConstants.LOG_PREFIX);
            task = handleOverflowTask(versionNo);
        }

        if (task == null) {
            log.debug("{} [EXECUTE-EMPTY] 当前没有待执行任务", CrawlerConstants.LOG_PREFIX);
            // 如果没有任务且当前状态是处理中，则标记为完成
            if (currentStatus == CrawlStatusEnum.PROCESSING) {
                versionManager.updateVersionToCompleted(currentVersion);
            }
            return;
        }

        // 执行任务
        executeTask(task);

        // 更新版本状态为处理中
        if (currentStatus == CrawlStatusEnum.PENDING) {
            versionManager.updateVersionToProcessing(currentVersion);
        }
    }
}
