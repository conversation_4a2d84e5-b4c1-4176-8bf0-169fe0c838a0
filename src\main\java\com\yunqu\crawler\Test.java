package com.yunqu.crawler;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.text.ParseException;
import java.util.*;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.yunqu.crawler.base.StringAmountUnitParser;
import com.yunqu.crawler.domain.vo.XtyTariffMobileRecordImportVo;
import com.yunqu.emergency.common.core.utils.DateUtils;
import com.yunqu.emergency.common.excel.core.ExcelListener;
import com.yunqu.emergency.common.excel.core.ExcelResult;
import com.yunqu.emergency.common.excel.utils.ExcelUtil;

/**
 * <p>
 *
 * </p>
 *
 * @ClassName Test
 * <AUTHOR> Copy This Tag)
 * @Description TODO 描述文件用途
 * @Since create in 2025/5/19 13:38
 * @Version v1.0
 * @Copyright Copyright (c) 2025
 * @Company 广州云趣信息科技有限公司
 */
public class Test {

    static String[] PARSE_PATTERNS = new String[]{"yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM", "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM", "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM", "yyyy年MM月dd日"};

    public static void main(String[] args) throws FileNotFoundException, ParseException {
        System.out.println(getCurrentDateId());
    }



    /**
     * 获取当前日期ID
     * 1-10号返回当月1号，11-20号返回当月11号，21号及以后返回当月21号
     */
    private static Integer getCurrentDateId() {
        java.util.Date nowDate = DateUtils.getNowDate();
        int dayOfMonth = DateUtil.dayOfMonth(nowDate);
        java.util.Date targetDate;

        if (dayOfMonth >= 1 && dayOfMonth <= 10) {
            // 1-10号返回当月1号
            targetDate = DateUtil.beginOfMonth(nowDate);
        } else if (dayOfMonth >= 11 && dayOfMonth <= 20) {
            // 11-20号返回当月11号
            targetDate = DateUtil.parse(DateUtil.format(nowDate, "yyyyMM") + "11");
        } else {
            // 21号及以后返回当月21号
            targetDate = DateUtil.parse(DateUtil.format(nowDate, "yyyyMM") + "21");
        }

        String dateStr = DateUtil.format(targetDate, "yyyyMMdd");
        return Convert.toInt(dateStr);
    }
}
