package com.yunqu.crawler.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yunqu.crawler.constants.CrawlerConstants;
import com.yunqu.crawler.domain.XtyCrawlerVersion;
import com.yunqu.crawler.domain.XtyCrawlerVersionConfig;
import com.yunqu.crawler.domain.bo.XtyCrawlerVersionBo;
import com.yunqu.crawler.enums.CrawlStatusEnum;
import com.yunqu.crawler.service.IXtyCrawlerVersionConfigService;
import com.yunqu.crawler.service.IXtyCrawlerVersionService;
import com.yunqu.crawler.util.CrawlerVersionUtils;
import com.yunqu.crawler.util.TableUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 爬虫版本管理器
 * 负责版本的创建、状态更新和查询
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CrawlerVersionManager {

    private final IXtyCrawlerVersionService versionService;
    private final IXtyCrawlerVersionConfigService versionConfigService;

    /**
     * 检查当天是否存在版本配置
     *
     * @return 是否存在配置
     */
    public boolean existsCurrentDayVersionConfig() {
        Date date = new Date();
        LambdaQueryWrapper<XtyCrawlerVersionConfig> wrapper = Wrappers.lambdaQuery(XtyCrawlerVersionConfig.class)
                .eq(XtyCrawlerVersionConfig::getRuleValue, CrawlerVersionUtils.getCurrentDay())
                .eq(XtyCrawlerVersionConfig::getIsEnabled, CrawlerConstants.CONFIG_ENABLED)
                .ge(XtyCrawlerVersionConfig::getEffectiveStartDate, date)
                .lt(XtyCrawlerVersionConfig::getEffectiveEndDate, date);
        boolean exists = versionConfigService.exists(wrapper);
        if (!exists) {
            wrapper = Wrappers.lambdaQuery(XtyCrawlerVersionConfig.class)
                    .eq(XtyCrawlerVersionConfig::getRuleValue, CrawlerVersionUtils.getCurrentDay())
                    .eq(XtyCrawlerVersionConfig::getIsEnabled, CrawlerConstants.CONFIG_ENABLED)
                    .eq(XtyCrawlerVersionConfig::getEffectiveType, CrawlerConstants.EFFECTIVE_TYPE_1);
            exists = versionConfigService.exists(wrapper);
        }
        return exists;
    }

    /**
     * 创建新版本
     *
     * @param dateId    日期ID
     * @param versionNo 版本号
     * @return 创建的版本对象
     */
    public XtyCrawlerVersion createVersion(Integer dateId, String versionNo) {
        long idx = CrawlerVersionUtils.generateVersionOrder();
        String crawlRecordTable = TableUtils.createCrawlRecordTable(versionNo);

        XtyCrawlerVersionBo version = new XtyCrawlerVersionBo();
        version.setVersionNo(versionNo);
        version.setIdx(idx);
        version.setBelongDateId(dateId);
        version.setVersionTableName(crawlRecordTable);

        versionService.insertByBo(version);

        log.info("{} [VERSION-CREATE] 成功创建版本: versionNo={}, table={}",
                CrawlerConstants.LOG_PREFIX, versionNo, crawlRecordTable);

        return getCurrentVersion();
    }

    /**
     * 获取当前活跃版本
     *
     * @return 当前版本，如果不存在返回null
     */
    public XtyCrawlerVersion getCurrentVersion() {
        LambdaUpdateWrapper<XtyCrawlerVersion> wrapper = Wrappers.lambdaUpdate(XtyCrawlerVersion.class)
                .eq(XtyCrawlerVersion::getVersionStatus, CrawlerConstants.VERSION_STATUS_ACTIVE)
                .in(XtyCrawlerVersion::getCrawlStatus,
                        CrawlStatusEnum.PENDING.getCode(),
                        CrawlStatusEnum.PROCESSING.getCode())
                .orderByDesc(XtyCrawlerVersion::getCreateTime);
        return versionService.getOne(wrapper);
    }

    /**
     * 更新版本状态为处理中
     *
     * @param version 版本对象
     */
    public void updateVersionToProcessing(XtyCrawlerVersion version) {
        if (version == null) {
            log.warn("{} [VERSION-UPDATE] 版本对象为空，无法更新状态", CrawlerConstants.LOG_PREFIX);
            return;
        }

        if (CrawlStatusEnum.PENDING.is(version.getCrawlStatus())) {
            version.setCrawlStatus(CrawlStatusEnum.PROCESSING.getCode());
            version.setCrawlStartTime(new Date());
            versionService.updateById(version);

            log.info("{} [VERSION-UPDATE] 版本状态更新为处理中: versionNo={}",
                    CrawlerConstants.LOG_PREFIX, version.getVersionNo());
        }
    }

    /**
     * 更新版本状态为已完成
     *
     * @param version 版本对象
     */
    public void updateVersionToCompleted(XtyCrawlerVersion version) {
        if (version == null) {
            log.warn("{} [VERSION-UPDATE] 版本对象为空，无法更新状态", CrawlerConstants.LOG_PREFIX);
            return;
        }

        if (CrawlStatusEnum.PROCESSING.is(version.getCrawlStatus())) {
            version.setCrawlStatus(CrawlStatusEnum.COMPLETED.getCode());
            version.setCrawlEndTime(new Date());
            versionService.updateById(version);

            log.info("{} [VERSION-UPDATE] 版本状态更新为已完成: versionNo={}",
                    CrawlerConstants.LOG_PREFIX, version.getVersionNo());
        }
    }

    /**
     * 检查版本是否处于指定状态
     *
     * @param version 版本对象
     * @param status  状态枚举
     * @return 是否匹配
     */
    public boolean isVersionInStatus(XtyCrawlerVersion version, CrawlStatusEnum status) {
        return version != null && status.is(version.getCrawlStatus());
    }
}
