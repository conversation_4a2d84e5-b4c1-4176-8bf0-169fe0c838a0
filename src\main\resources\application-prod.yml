--- # 数据源配置
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content
    dynamic:
      # 性能分析插件(有性能损耗 不建议生产环境使用)
      p6spy: true
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      # 严格模式 匹配不到数据源则报错
      strict: true
      datasource:
        # 主库数据源
        master:
          type: ${spring.datasource.type}
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ******************************************************************************************************************************************************************************************************************************************************************
          username: root
          password: 2Ao*=eFHR<cN
        # 从库数据源
        slave:
          lazy: true
          type: ${spring.datasource.type}
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ******************************************************************************************************************************************************************************************************************************************************************
          username: root
          password: 2Ao*=eFHR<cN
      hikari:
        # 最大连接池数量
        maxPoolSize: 20
        # 最小空闲线程数量
        minIdle: 10
        # 配置获取连接等待超时的时间
        connectionTimeout: 30000
        # 校验超时时间
        validationTimeout: 5000
        # 空闲连接存活最大时间，默认10分钟
        idleTimeout: 600000
        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟
        maxLifetime: 1800000
        # 多久检查一次连接的活性
        keepaliveTime: 30000

--- # redis 单机配置(单机与集群只能开启一个另一个需要注释掉)
spring.data:
  redis:
    # 哨兵模式配置
    sentinel:
      # 主节点名称
      master: mymaster
      # 哨兵节点
      nodes:
        - 192.168.102.13:26379
        - 192.168.102.14:26379
        - 192.168.102.15:26379
    # 数据库索引
    database: 1
    # redis密码
    password: lCGJAgzjwf5F
    # 连接超时时间
    timeout: 10s
    # 是否开启ssl
    ssl.enabled: false

# redisson 配置
redisson:
  threads: 16
  nettyThreads: 32
  sentinelServersConfig:
    clientName: ${ruoyi.name}
    masterName: mymaster
    masterConnectionMinimumIdleSize: 32
    masterConnectionPoolSize: 64
    slaveConnectionMinimumIdleSize: 32
    slaveConnectionPoolSize: 64
    idleConnectionTimeout: 30000
    connectTimeout: 10000
    timeout: 6000
    retryAttempts: 3
    retryInterval: 1500
    failedSlaveReconnectionInterval: 3000
    failedSlaveCheckInterval: 60000
    subscriptionConnectionPoolSize: 50
    readMode: "SLAVE"
    subscriptionMode: "MASTER"
    dnsMonitoringInterval: 5000


# 日志配置
logging:
  level:
    com.yunqu.crawler: debug
    org.springframework: warn
    #org.redisson: debug
    org.mybatis.spring.mapper: error
    org.apache.fury: warn
  config: classpath:logback-plus.xml

file:
  # 文件上传路径，默认为当前项目根目录
  tmp-path: /home/<USER>/fileserver

crawler:
  # 爬虫配置
  local-server: http://**************:8088
  base-url: http://***************:8123
  unicom-req-base-url: https://**************:8068
  run-task-lock-key: excueteCrawlerTask
  operator: telecom,mobile,unicom,broadnet
  overflow-operator: unicom
  server-name: node20
  telecom-provinces-name: 北京,安徽,重庆,福建,广东,甘肃,广西,贵州,湖北,湖南,河北,河南,海南,黑龙江,江苏,吉林,江西,辽宁,内蒙古,宁夏,青海,山东,上海,山西,陕西,四川,天津,新疆,西藏,云南,浙江
  telecom-provinces: bj,ah,cq,fj,gd,gs,gx,gz,hb,hn,he,ha,hi,hl,js,jl,jx,ln,nm,nx,qh,sd,sh,sx,sn,sc,tj,xj,xz,yn,zj
  # telecom-provinces-name: 北京,安徽,重庆,福建,广东,甘肃,广西,贵州,湖北,湖南,河北,河南,海南,黑龙江,江苏,吉林,江西,辽宁,内蒙古,宁夏,青海,山东,上海,山西,陕西,四川,天津,新疆,云南,浙江,新疆
  # telecom-provinces: bj,ah,cq,fj,gd,gs,gx,gz,hb,hn,he,ha,hi,hl,js,jl,jx,ln,nm,nx,qh,sd,sh,sx,sn,sc,tj,xj,yn,zj,xz
  mobile-provinces-name: 北京,广东,上海,天津,重庆,辽宁,江苏,湖北,四川,陕西,河北,山西,河南,吉林,黑龙江,内蒙古,山东,安徽,浙江,福建,湖南,广西,江西,贵州,云南,西藏,海南,甘肃,宁夏,青海,新疆
  mobile-provinces: 100,200,210,220,230,240,250,270,280,290,311,351,371,431,451,471,531,551,571,591,731,771,791,851,871,891,898,931,951,971,991
  unicom-provinces-name: 北京,安徽,重庆,福建,甘肃,广东,广西,贵州,海南,河北,河南,黑龙江,湖北,湖南,吉林,江苏,江西,辽宁,内蒙古,宁夏,青海,山东,山西,陕西,上海,四川,天津,西藏,新疆,云南,浙江
  unicom-provinces: 011,030,083,038,087,051,059,085,050,018,076,097,071,074,090,034,075,091,010,088,070,017,019,084,031,081,013,079,089,086,036
  gdt-provinces-name: 安徽,北京,重庆,福建,广东,广州,深圳,甘肃,广西,贵州,河北,湖北,黑龙江,湖南,河南,海南,吉林,江苏,江西,辽宁,内蒙古,宁夏,青海,四川,山东,上海,山西,陕西,天津,新疆,西藏,云南,浙江
  gdt-provinces: 安徽省,北京市,重庆市,福建省,广东省,广州市,深圳市,甘肃省,广西壮族自治区,贵州省,河北省,湖北省,黑龙江省,湖南省,河南省,海南省,吉林省,江苏省,江西省,辽宁省,内蒙古自治区,宁夏回族自治区,青海省,四川省,山东省,上海市,山西省,陕西省,天津市,新疆维吾尔自治区,西藏自治区,云南省,浙江省


