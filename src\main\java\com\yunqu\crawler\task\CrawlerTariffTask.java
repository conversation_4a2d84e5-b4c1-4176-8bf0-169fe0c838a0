package com.yunqu.crawler.task;

import com.baomidou.lock.annotation.Lock4j;
import com.yunqu.crawler.config.CrawlerTaskProperties;
import com.yunqu.crawler.constants.CrawlerConstants;
import com.yunqu.crawler.domain.XtyCrawlerVersion;
import com.yunqu.crawler.manager.CrawlerVersionManager;
import com.yunqu.crawler.service.ICrawlerTariffTaskService;
import com.yunqu.crawler.service.IXtyCrawlerTaskService;
import com.yunqu.crawler.util.CrawlerVersionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 运营商资费信息爬虫定时任务
 *
 * 主要功能：
 * 1. 定时触发各运营商资费信息的爬取任务
 * 2. 定时执行待处理的爬虫任务
 * 3. 管理任务执行状态和并发控制
 *
 * 执行策略：
 * - 资费爬取任务：每天凌晨1点执行一次
 * - 任务执行检查：每分钟执行一次
 *
 * <AUTHOR>
 * @version 2.0
 */
@RequiredArgsConstructor
@Slf4j
@Component
public class CrawlerTariffTask {

    private final IXtyCrawlerTaskService crawlerTaskService;
    private final CrawlerVersionManager versionManager;
    private final CrawlerTaskProperties taskProperties;
    private final ICrawlerTariffTaskService crawlerTariffTaskService;

    /**
     * 爬取资费信息的主任务
     * 按顺序执行各运营商的资费爬取
     * 执行时间：每天凌晨1点执行一次
     */
    @Lock4j(name = CrawlerConstants.LOCK_CRAWLER_TARIFF)
    @Scheduled(cron = "0 0 1 * * ?")
    public void crawlerTariff() {
        log.info("{} [MAIN-START] ========== 开始执行资费信息爬取主任务 ==========", CrawlerConstants.LOG_PREFIX);
        long startTime = System.currentTimeMillis();

        try {
            if (!versionManager.existsCurrentDayVersionConfig()) {
                log.info("{} [MAIN-INFO] 当前无执行版本配置, 跳过执行", CrawlerConstants.LOG_PREFIX);
                return;
            }

            // 获取当前日期ID
            Integer dateId = CrawlerVersionUtils.getCurrentDateId();
            log.info("{} [MAIN-INFO] 当前执行日期: {}", CrawlerConstants.LOG_PREFIX, dateId);

            // 幂等性检查：检查当前日期是否已存在任务
            if (crawlerTaskService.existsTasksByDate(dateId)) {
                log.info("{} [MAIN-SKIP] 当前日期的任务已存在，跳过执行, dateId: {}",
                        CrawlerConstants.LOG_PREFIX, dateId);
                return;
            }

            // 生成版本号
            String versionNo = CrawlerVersionUtils.generateVersion(dateId);

            // 按顺序执行各运营商的爬取任务
            crawlerTariffTaskService.executeOperatorCrawler(dateId, versionNo);

            log.info("{} [MAIN-END] ========== 资费信息爬取主任务执行完成, 总耗时: {}ms ==========",
                    CrawlerConstants.LOG_PREFIX, System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("{} [MAIN-FAIL] 资费信息爬取主任务执行异常: {}", CrawlerConstants.LOG_PREFIX, e.getMessage(), e);
        }
    }



    /**
     * 执行待处理的爬虫任务
     * 执行频率：每分钟检查一次
     * 执行策略：
     * 1. 检查是否有正在运行的任务
     * 2. 获取待执行的任务
     * 3. 执行任务并更新状态
     */
    @Lock4j(name = CrawlerConstants.LOCK_EXECUTE_CRAWLER_TASK)
    @Scheduled(cron = "0 0/1 * * * ?")
    public void executeCrawlerTask() {
        log.debug("{} [EXECUTE-START] ========== 开始检查待执行爬虫任务 ==========", CrawlerConstants.LOG_PREFIX);
        long startTime = System.currentTimeMillis();

        try {
            if (!taskProperties.hasOperator()) {
                return;
            }

            // 检查是否有正在运行的任务
            if (crawlerTariffTaskService.isTaskRunning()) {
                log.debug("{} [EXECUTE-SKIP] 当前有任务正在运行中，跳过本次检查, 耗时: {}ms",
                        CrawlerConstants.LOG_PREFIX, System.currentTimeMillis() - startTime);
                return;
            }

            XtyCrawlerVersion currentVersion = versionManager.getCurrentVersion();
            if (currentVersion == null) {
                log.debug("{} [EXECUTE-EMPTY] 当前没有版本信息, 耗时: {}ms",
                        CrawlerConstants.LOG_PREFIX, System.currentTimeMillis() - startTime);
                return;
            }

            String versionNo = currentVersion.getVersionNo();

            // 处理任务执行完成后的版本状态更新
            crawlerTariffTaskService.handleTaskCompletion(currentVersion, taskProperties.getOperatorList(), versionNo);

        } catch (Exception e) {
            log.error("{} [EXECUTE-FAIL] 执行爬虫任务异常: {}", CrawlerConstants.LOG_PREFIX, e.getMessage(), e);
        } finally {
            log.debug("{} [EXECUTE-END] 完成爬虫任务执行检查, 耗时: {}ms",
                    CrawlerConstants.LOG_PREFIX, System.currentTimeMillis() - startTime);
        }
    }


}
